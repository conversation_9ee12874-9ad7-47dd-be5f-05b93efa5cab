import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, <PERSON>umn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index } from "typeorm";
import { ReviewStatus } from "../enums";
import { Work } from "../../../works/models/entities";
import { User } from "src/modules/user/models/entities/user.entity";

@Entity("reviews")
@Index(["userId", "workId"], { unique: true })
export class Review {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ type: "uuid" })
	workId: string;

	@Column({ type: "integer" })
	userId: number;

	@Column({ type: "integer", width: 1 })
	rating: number;

	@Column({ type: "text", nullable: true })
	comment?: string;

	@Column({
		type: "enum",
		enum: ReviewStatus,
		default: ReviewStatus.ACTIVE,
	})
	status: ReviewStatus;

	@Column({ type: "integer", default: 0 })
	likesCount: number;

	@Column({ type: "integer", default: 0 })
	dislikesCount: number;

	@ManyToOne(() => Work, { onDelete: "CASCADE" })
	@JoinColumn({ name: "workId" })
	work: Work;

	@ManyToOne(() => User, { onDelete: "CASCADE" })
	@JoinColumn({ name: "userId" })
	user: User;

	@CreateDateColumn()
	createdAt: Date;

	@UpdateDateColumn()
	updatedAt: Date;
}
