import { Inject, Injectable, NotFoundException, ConflictException } from "@nestjs/common";
import { UpdateTagDto } from "../../models/dtos";
import { ITag, ITagRepository } from "../../models/interfaces";

@Injectable()
export class UpdateTagUseCase {
	constructor(
		@Inject("ITagRepository")
		private tagRepository: ITagRepository
	) {}

	async execute(id: string, updateTagDto: UpdateTagDto): Promise<ITag> {
		const tag = await this.tagRepository.findById(id);

		if (!tag) {
			throw new NotFoundException("Tag não encontrada");
		}

		// Se estiver atualizando o nome, verificar se já existe uma tag com o mesmo nome
		if (updateTagDto.name && updateTagDto.name !== tag.name) {
			const existingTag = await this.tagRepository.findByName(updateTagDto.name);

			if (existingTag) {
				throw new ConflictException(`J<PERSON> existe uma tag com o nome '${updateTagDto.name}'`);
			}
		}

		return await this.tagRepository.update(id, updateTagDto);
	}
}
