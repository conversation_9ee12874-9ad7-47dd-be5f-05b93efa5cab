import { Injectable, NotFoundException } from "@nestjs/common";
import { WorkRepository } from "../../repositories";
import { IWork } from "../../models/interfaces";

@Injectable()
export class GetWorkUseCase {
	constructor(private readonly workRepository: WorkRepository) {}

	async execute(id: string): Promise<IWork> {
		const work = await this.workRepository.findById(id);
		if (!work) {
			throw new NotFoundException("Obra não encontrada");
		}

		return work;
	}
}
