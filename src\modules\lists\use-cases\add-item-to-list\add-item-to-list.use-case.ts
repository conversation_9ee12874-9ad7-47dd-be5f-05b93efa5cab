import { Injectable, NotFoundException, ConflictException, Inject } from "@nestjs/common";
import { IListRepository, IListItemRepository, ICreateListItemRequest, IListItem } from "../../models/interfaces";
import { WorkRepository } from "../../../works/repositories";

@Injectable()
export class AddItemToListUseCase {
	constructor(
		@Inject("IListRepository")
		private readonly listRepository: IListRepository,
		@Inject("IListItemRepository")
		private readonly listItemRepository: IListItemRepository,
		private readonly workRepository: WorkRepository
	) {}

	async execute(userId: string, listId: string, data: Omit<ICreateListItemRequest, "listId">): Promise<IListItem> {
		// Verificar se a lista existe e pertence ao usuário
		const list = await this.listRepository.findById(listId);
		if (!list) {
			throw new NotFoundException("Lista não encontrada");
		}

		if (list.userId !== userId) {
			throw new NotFoundException("Lista não encontrada");
		}

		// Verificar se a obra existe
		const work = await this.workRepository.findById(data.workId);
		if (!work) {
			throw new NotFoundException("Obra não encontrada");
		}

		// Verificar se a obra já não está na lista
		const existingItem = await this.listItemRepository.findByListAndWork(listId, data.workId);
		if (existingItem) {
			throw new ConflictException("Esta obra já está na lista");
		}

		const createData: ICreateListItemRequest = {
			...data,
			listId,
		};

		const listItem = await this.listItemRepository.create(createData);

		// Incrementar contador de itens na lista
		await this.listRepository.incrementItemsCount(listId);

		return listItem;
	}
}
