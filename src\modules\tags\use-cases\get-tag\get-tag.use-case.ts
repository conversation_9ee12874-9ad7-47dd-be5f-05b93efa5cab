import { Inject, Injectable, NotFoundException } from "@nestjs/common";
import { ITag, ITagRepository } from "../../models/interfaces";

@Injectable()
export class GetTagUseCase {
	constructor(
		@Inject("ITagRepository")
		private tagRepository: ITagRepository
	) {}

	async execute(id: string): Promise<ITag> {
		const tag = await this.tagRepository.findById(id);

		if (!tag) {
			throw new NotFoundException("Tag não encontrada");
		}

		return tag;
	}
}
