import { Injectable, ConflictException, NotFoundException, Inject } from "@nestjs/common";
import { IUserWorkRepository, ICreateUserWorkRequest, IUserWork } from "../../models/interfaces";
import { WorkRepository } from "../../../works/repositories";

@Injectable()
export class AddToReadingUseCase {
	constructor(
		@Inject("IUserWorkRepository")
		private readonly userWorkRepository: IUserWorkRepository,
		private readonly workRepository: WorkRepository
	) {}

	async execute(userId: string, data: ICreateUserWorkRequest): Promise<IUserWork> {
		// Verificar se a obra existe
		const work = await this.workRepository.findById(data.workId);
		if (!work) {
			throw new NotFoundException("Obra não encontrada");
		}

		// Verificar se o usuário já não tem esta obra em sua lista
		const existingUserWork = await this.userWorkRepository.findByUserAndWork(userId, data.workId);
		if (existingUserWork) {
			throw new ConflictException("Esta obra já está na sua lista de leitura");
		}

		const createData: ICreateUserWorkRequest = {
			...data,
			userId,
		};

		return await this.userWorkRepository.create(createData);
	}
}
