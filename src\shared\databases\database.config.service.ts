import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from "@nestjs/typeorm";

@Injectable()
export class DatabaseConfigService implements TypeOrmOptionsFactory {
	constructor(private configService: ConfigService) {}

	createTypeOrmOptions(): TypeOrmModuleOptions {
		return {
			type: "postgres",
			host: this.configService.get<string>("DATABASE_HOST"),
			port: this.configService.get<number>("DATABASE_PORT"),
			username: this.configService.get<string>("DATABASE_USERNAME"),
			password: this.configService.get<string>("DATABASE_PASSWORD"),
			database: this.configService.get<string>("DATABASE_NAME"),
			entities: [__dirname + "/../../**/*.entity.{js,ts}"],
			synchronize: true, // Habilitado temporariamente para criar as novas estruturas
			dropSchema: true, // Vai dropar e recriar o schema
			migrations: [__dirname + "/../../migrations/*.{js,ts}"],
			migrationsRun: false,
		};
	}
}
