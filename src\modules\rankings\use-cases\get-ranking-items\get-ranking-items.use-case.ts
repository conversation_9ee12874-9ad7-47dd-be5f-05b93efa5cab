import { Inject, Injectable, NotFoundException } from "@nestjs/common";
import { IRankingItem, IRankingItemRepository, IRankingRepository } from "../../models/interfaces";
import { RankingItemFiltersDto } from "../../models/dtos";

@Injectable()
export class GetRankingItemsUseCase {
	constructor(
		@Inject("IRankingRepository")
		private rankingRepository: IRankingRepository,

		@Inject("IRankingItemRepository")
		private rankingItemRepository: IRankingItemRepository
	) {}

	async execute(rankingId: string, userId: string | null, filters?: RankingItemFiltersDto): Promise<IRankingItem[]> {
		// Verificar se o ranking existe e se é acessível ao usuário
		const ranking = await this.rankingRepository.findById(rankingId);

		if (!ranking) {
			throw new NotFoundException("Ranking não encontrado");
		}

		// Se o ranking não for público e não pertencer ao usuário, não permita acesso
		if (!ranking.isPublic && ranking.userId !== userId) {
			throw new NotFoundException("Ranking não encontrado");
		}

		// Buscar os itens do ranking com filtros aplicados
		let parsedFilters: any = filters;
		if (filters) {
			parsedFilters = {
				...filters,
				sortBy: (["position", "rating", "createdAt"].includes(filters.sortBy as string) ? filters.sortBy : undefined) as
					| "position"
					| "rating"
					| "createdAt"
					| undefined,
				sortOrder: (["ASC", "DESC"].includes((filters.sortOrder as string)?.toUpperCase())
					? (filters.sortOrder as string).toUpperCase()
					: undefined) as "ASC" | "DESC" | undefined,
			};
		}
		return await this.rankingItemRepository.findByRankingId(rankingId, parsedFilters);
	}
}
