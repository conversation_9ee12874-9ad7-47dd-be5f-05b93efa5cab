import { Injectable, NotFoundException, Inject } from "@nestjs/common";
import { IListRepository, IListItemRepository, IListItemFilters, IListItem } from "../../models/interfaces";

export interface IGetListItemsResponse {
	items: IListItem[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
}

@Injectable()
export class GetListItemsUseCase {
	constructor(
		@Inject("IListRepository")
		private readonly listRepository: IListRepository,
		@Inject("IListItemRepository")
		private readonly listItemRepository: IListItemRepository
	) {}

	async execute(userId: string, listId: string, filters: Omit<IListItemFilters, "listId">): Promise<IGetListItemsResponse> {
		// Verificar se a lista existe
		const list = await this.listRepository.findById(listId);
		if (!list) {
			throw new NotFoundException("Lista não encontrada");
		}

		// Se a lista não é pública, verificar se pertence ao usuário
		if (!list.isPublic && list.userId !== userId) {
			throw new NotFoundException("Lista não encontrada");
		}

		const filtersWithListId: IListItemFilters = {
			...filters,
			listId,
		};

		const { items, total } = await this.listItemRepository.findByList(filtersWithListId);

		const page = filters.page || 1;
		const limit = filters.limit || 20;
		const totalPages = Math.ceil(total / limit);

		return {
			items,
			total,
			page,
			limit,
			totalPages,
		};
	}
}
