import { Injectable, NotFoundException, Inject } from "@nestjs/common";
import { IListRepository, IList } from "../../models/interfaces";

@Injectable()
export class GetListUseCase {
	constructor(
		@Inject("IListRepository")
		private readonly listRepository: IListRepository
	) {}

	async execute(userId: string, listId: string): Promise<IList> {
		const list = await this.listRepository.findById(listId);
		if (!list) {
			throw new NotFoundException("Lista não encontrada");
		}

		// Se a lista não é pública, verificar se pertence ao usuário
		if (!list.isPublic && list.userId !== userId) {
			throw new NotFoundException("Lista não encontrada");
		}

		return list;
	}
}
