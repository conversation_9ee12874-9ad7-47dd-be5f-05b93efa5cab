import { Injectable, Inject } from "@nestjs/common";
import { IReviewRepository, IReviewStats } from "../../models/interfaces";

@Injectable()
export class GetReviewStatsUseCase {
	constructor(
		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository,
	) {}

	async execute(workId: string): Promise<IReviewStats> {
		return await this.reviewRepository.getStats(workId);
	}
}
