import { Injectable, NotFoundException, ConflictException } from "@nestjs/common";
import { WorkRepository, ChapterRepository } from "../../repositories";
import { ICreateChapterRequest, IChapter } from "../../models/interfaces";

@Injectable()
export class CreateChapterUseCase {
	constructor(
		private readonly workRepository: WorkRepository,
		private readonly chapterRepository: ChapterRepository
	) {}

	async execute(data: ICreateChapterRequest): Promise<IChapter> {
		// Verificar se a obra existe
		const work = await this.workRepository.findById(data.workId);
		if (!work) {
			throw new NotFoundException("Obra não encontrada");
		}

		// Verificar se já existe um capítulo com o mesmo número para esta obra
		const existingChapter = await this.chapterRepository.findByWorkIdAndNumber(data.workId, data.number);
		if (existingChapter) {
			throw new ConflictException(`Capítulo ${data.number} já existe para esta obra`);
		}

		return await this.chapterRepository.create(data);
	}
}
