import { Injectable, NotFoundException, ConflictException, Inject } from "@nestjs/common";
import { IListRepository, IUpdateListRequest, IList } from "../../models/interfaces";

@Injectable()
export class UpdateListUseCase {
	constructor(
		@Inject("IListRepository")
		private readonly listRepository: IListRepository
	) {}

	async execute(userId: string, listId: string, data: IUpdateListRequest): Promise<IList> {
		// Verificar se a lista existe e pertence ao usuário
		const existingList = await this.listRepository.findById(listId);
		if (!existingList) {
			throw new NotFoundException("Lista não encontrada");
		}

		if (existingList.userId !== userId) {
			throw new NotFoundException("Lista não encontrada");
		}

		// Se está tentando alterar o nome, verificar se não existe outra lista com o mesmo nome
		if (data.name && data.name !== existingList.name) {
			const listWithSameName = await this.listRepository.findByUserAndName(userId, data.name);
			if (listWithSameName) {
				throw new ConflictException("Já existe uma lista com este nome");
			}
		}

		return await this.listRepository.update(listId, data);
	}
}
