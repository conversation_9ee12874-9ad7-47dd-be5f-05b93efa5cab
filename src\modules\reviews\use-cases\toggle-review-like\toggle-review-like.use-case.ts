import { Injectable, NotFoundException, Inject } from "@nestjs/common";
import { IReviewRepository } from "../../models/interfaces";

@Injectable()
export class ToggleReviewLikeUseCase {
	constructor(
		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository,
	) {}

	async execute(reviewId: string, isLike: boolean): Promise<void> {
		const review = await this.reviewRepository.findById(reviewId);

		if (!review) {
			throw new NotFoundException("Review não encontrado");
		}

		if (isLike) {
			await this.reviewRepository.incrementLikes(reviewId);
		} else {
			await this.reviewRepository.decrementLikes(reviewId);
		}
	}
}
