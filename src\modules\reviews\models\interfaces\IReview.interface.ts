import { ReviewStatus } from "../enums";

export interface IReview {
	id: string;
	workId: string;
	userId: number;
	rating: number;
	comment?: string;
	status: ReviewStatus;
	likesCount: number;
	dislikesCount: number;
	createdAt: Date;
	updatedAt: Date;
}

export interface ICreateReviewRequest {
	workId: string;
	userId: number;
	rating: number;
	comment?: string;
}

export interface IUpdateReviewRequest {
	rating?: number;
	comment?: string;
}

export interface IReviewFilters {
	workId?: string;
	userId?: number;
	rating?: number;
	status?: ReviewStatus;
	sortBy?: "createdAt" | "rating" | "likesCount";
	sortOrder?: "ASC" | "DESC";
	page?: number;
	limit?: number;
}

export interface IReviewStats {
	totalReviews: number;
	averageRating: number;
	ratingDistribution: {
		[key: number]: number;
	};
}
