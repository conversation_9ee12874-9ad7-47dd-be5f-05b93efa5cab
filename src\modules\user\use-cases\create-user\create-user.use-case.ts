import { Injectable, ConflictException, Inject } from "@nestjs/common";
import * as bcrypt from "bcryptjs";
import { IUserRepository } from "../../models/interfaces/user-repository.interface";
import { CreateUserDto } from "../../models/dtos/create-user.dto";
import { UserResponseDto } from "../../models/dtos/user-response.dto";
import { User } from "../../models/entities/user.entity";

@Injectable()
export class CreateUserUseCase {
	constructor(
		@Inject("IUserRepository")
		private readonly userRepository: IUserRepository
	) {}

	async execute(createUserDto: CreateUserDto): Promise<UserResponseDto> {
		// Verificar se email já existe
		const existingEmail = await this.userRepository.findByEmail(createUserDto.email);
		if (existingEmail) {
			throw new ConflictException("Email já está em uso");
		}

		// Verificar se username já existe
		const existingUsername = await this.userRepository.findByUsername(createUserDto.username);
		if (existingUsername) {
			throw new ConflictException("Nome de usuário já está em uso");
		}

		// Hash da senha
		const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

		// Criar usuário
		const userData = {
			...createUserDto,
			password: hashedPassword,
		};

		const user = await this.userRepository.create(userData);
		return this.mapToResponseDto(user);
	}

	private mapToResponseDto(user: User): UserResponseDto {
		const { password, ...userWithoutPassword } = user;
		return userWithoutPassword as UserResponseDto;
	}
}
