import { Injectable, NotFoundException, Inject } from "@nestjs/common";
import { IUserRepository } from "../../models/interfaces/user-repository.interface";
import { UserResponseDto } from "../../models/dtos/user-response.dto";
import { User } from "../../models/entities/user.entity";

@Injectable()
export class FindUserByIdUseCase {
	constructor(
		@Inject("IUserRepository")
		private readonly userRepository: IUserRepository
	) {}

	async execute(id: number): Promise<UserResponseDto> {
		const user = await this.userRepository.findById(id);
		if (!user) {
			throw new NotFoundException("Usuário não encontrado");
		}
		return this.mapToResponseDto(user);
	}

	private mapToResponseDto(user: User): UserResponseDto {
		const { password, ...userWithoutPassword } = user;
		return userWithoutPassword as UserResponseDto;
	}
}
