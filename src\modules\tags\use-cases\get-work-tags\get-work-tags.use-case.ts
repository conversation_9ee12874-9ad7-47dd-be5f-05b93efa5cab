import { Inject, Injectable, NotFoundException } from "@nestjs/common";
import { IWorkTag, IWorkTagRepository } from "../../models/interfaces";

@Injectable()
export class GetWorkTagsUseCase {
	constructor(
		@Inject("IWorkTagRepository")
		private workTagRepository: IWorkTagRepository,

		@Inject("IWorkRepository")
		private workRepository: any // Usando any pois o IWorkRepository não foi importado
	) {}

	async execute(workId: string): Promise<IWorkTag[]> {
		// Verificar se a obra existe
		const work = await this.workRepository.findById(workId);

		if (!work) {
			throw new NotFoundException("Obra não encontrada");
		}

		// Buscar todas as tags associadas à obra
		return await this.workTagRepository.findByWorkId(workId);
	}
}
