import { Injectable, Inject, UnauthorizedException, NotFoundException } from "@nestjs/common";
import * as bcrypt from "bcryptjs";
import { IAuthRepository } from "../../models/interfaces/auth-repository.interface";
import { ChangePasswordDto } from "../../models/dtos/change-password.dto";
import { ICurrentUserPayload } from "../../../../shared/decorators/current-user.decorator";

@Injectable()
export class ChangePasswordUseCase {
	constructor(
		@Inject("IAuthRepository")
		private readonly authRepository: IAuthRepository
	) {}

	async execute(currentUser: ICurrentUserPayload, changePasswordDto: ChangePasswordDto): Promise<{ message: string }> {
		// Buscar o usuário atual
		const user = await this.authRepository.findByUsernameOrEmail(currentUser.username);
		if (!user) {
			throw new NotFoundException("<PERSON>u<PERSON>rio não encontrado");
		}

		// Verificar se a senha atual está correta
		const isCurrentPasswordValid = await bcrypt.compare(changePasswordDto.currentPassword, user.password);
		if (!isCurrentPasswordValid) {
			throw new UnauthorizedException("Senha atual incorreta");
		}

		// Verificar se a nova senha é diferente da atual
		const isSamePassword = await bcrypt.compare(changePasswordDto.newPassword, user.password);
		if (isSamePassword) {
			throw new UnauthorizedException("A nova senha deve ser diferente da senha atual");
		}

		// Criptografar a nova senha
		const saltRounds = 10;
		const hashedNewPassword = await bcrypt.hash(changePasswordDto.newPassword, saltRounds);

		// Atualizar a senha no banco de dados
		await this.authRepository.updatePassword(user.id, hashedNewPassword);

		// Invalidar todos os refresh tokens existentes por segurança
		await this.authRepository.updateRefreshToken(user.id, null);

		return {
			message: "Senha alterada com sucesso. Faça login novamente com a nova senha.",
		};
	}
}
