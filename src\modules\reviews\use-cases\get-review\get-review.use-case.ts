import { Injectable, NotFoundException, Inject } from "@nestjs/common";
import { IReviewRepository, IReview } from "../../models/interfaces";

@Injectable()
export class GetReviewUseCase {
	constructor(
		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository,
	) {}

	async execute(id: string): Promise<IReview> {
		const review = await this.reviewRepository.findById(id);

		if (!review) {
			throw new NotFoundException("Review não encontrado");
		}

		return review;
	}
}
