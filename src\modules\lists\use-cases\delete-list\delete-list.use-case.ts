import { Injectable, NotFoundException, Inject } from "@nestjs/common";
import { IListRepository } from "../../models/interfaces";

@Injectable()
export class DeleteListUseCase {
	constructor(
		@Inject("IListRepository")
		private readonly listRepository: IListRepository
	) {}

	async execute(userId: string, listId: string): Promise<void> {
		// Verificar se a lista existe e pertence ao usuário
		const existingList = await this.listRepository.findById(listId);
		if (!existingList) {
			throw new NotFoundException("Lista não encontrada");
		}

		if (existingList.userId !== userId) {
			throw new NotFoundException("Lista não encontrada");
		}

		await this.listRepository.delete(listId);
	}
}
