import { Injectable, NotFoundException, ForbiddenException, Inject } from "@nestjs/common";
import { IReviewRepository, IReview, IUpdateReviewRequest } from "../../models/interfaces";

@Injectable()
export class UpdateReviewUseCase {
	constructor(
		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository
	) {}

	async execute(id: string, userId: string, data: IUpdateReviewRequest): Promise<IReview> {
		const review = await this.reviewRepository.findById(id);

		if (!review) {
			throw new NotFoundException("Review não encontrado");
		}

		if (review.userId !== Number(userId)) {
			throw new ForbiddenException("Você só pode editar seus próprios reviews");
		}

		return await this.reviewRepository.update(id, data);
	}
}
