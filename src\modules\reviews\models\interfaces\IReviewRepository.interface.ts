import { IReview, ICreateReviewRequest, IUpdateReviewRequest, IReviewFilters, IReviewStats } from './IReview.interface';

export interface IReviewRepository {
	create(data: ICreateReviewRequest): Promise<IReview>;
	findById(id: string): Promise<IReview | null>;
	findByUserAndWork(userId: string, workId: string): Promise<IReview | null>;
	findMany(filters: IReviewFilters): Promise<IReview[]>;
	update(id: string, data: IUpdateReviewRequest): Promise<IReview>;
	delete(id: string): Promise<void>;
	getStats(workId: string): Promise<IReviewStats>;
	incrementLikes(id: string): Promise<void>;
	decrementLikes(id: string): Promise<void>;
	incrementDislikes(id: string): Promise<void>;
	decrementDislikes(id: string): Promise<void>;
}
