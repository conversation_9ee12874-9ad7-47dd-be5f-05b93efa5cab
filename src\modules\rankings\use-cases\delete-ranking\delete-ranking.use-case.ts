import { Inject, Injectable, NotFoundException } from "@nestjs/common";
import { IRankingRepository } from "../../models/interfaces";

@Injectable()
export class DeleteRankingUseCase {
	constructor(
		@Inject("IRankingRepository")
		private rankingRepository: IRankingRepository
	) {}

	async execute(id: string, userId: string): Promise<void> {
		const existingRanking = await this.rankingRepository.findById(id);

		if (!existingRanking) {
			throw new NotFoundException("Ranking não encontrado");
		}

		// Verificar se o ranking pertence ao usuário
		if (existingRanking.userId !== userId) {
			throw new NotFoundException("Ranking não encontrado");
		}

		await this.rankingRepository.delete(id);
	}
}
