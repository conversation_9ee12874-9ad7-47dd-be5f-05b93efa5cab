import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Review } from "./models/entities";
import { ReviewRepository } from "./repositories";
import {
	CreateReviewUseCase,
	GetReviewUseCase,
	ListReviewsUseCase,
	UpdateReviewUseCase,
	DeleteReviewUseCase,
	GetReviewStatsUseCase,
	ToggleReviewLikeUseCase,
} from "./use-cases";
import { ReviewsController } from "./controllers";

@Module({
	imports: [TypeOrmModule.forFeature([Review])],
	controllers: [ReviewsController],
	providers: [
		ReviewRepository,
		CreateReviewUseCase,
		GetReviewUseCase,
		ListReviewsUseCase,
		UpdateReviewUseCase,
		DeleteReviewUseCase,
		GetReviewStatsUseCase,
		ToggleReviewLikeUseCase,
		{
			provide: "IReviewRepository",
			useExisting: ReviewRepository,
		},
	],
	exports: [ReviewRepository, "IReviewRepository"],
})
export class ReviewsModule {}
