import { Injectable, Inject } from "@nestjs/common";
import * as bcrypt from "bcryptjs";
import { IAuthRepository } from "../../models/interfaces/auth-repository.interface";

@Injectable()
export class ValidateUserUseCase {
	constructor(
		@Inject("IAuthRepository")
		private readonly authRepository: IAuthRepository
	) {}

	async execute(usernameOrEmail: string, password: string): Promise<any> {
		const user = await this.authRepository.findByUsernameOrEmail(usernameOrEmail);
		if (user && (await bcrypt.compare(password, user.password))) {
			const { password, ...result } = user;
			return result;
		}
		return null;
	}
}
