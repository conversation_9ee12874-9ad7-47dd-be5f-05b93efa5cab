import { IsOptional, IsU<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, IsString, IsN<PERSON><PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ReviewStatus } from '../enums';

export class ReviewFiltersDto {
	@IsOptional()
	@IsUUID()
	workId?: string;

	@IsOptional()
	@IsUUID()
	userId?: string;

	@IsOptional()
	@IsInt()
	@Min(1)
	@Max(5)
	@Type(() => Number)
	rating?: number;

	@IsOptional()
	@IsEnum(ReviewStatus)
	status?: ReviewStatus;

	@IsOptional()
	@IsString()
	sortBy?: 'createdAt' | 'rating' | 'likesCount';

	@IsOptional()
	@IsString()
	sortOrder?: 'ASC' | 'DESC';

	@IsOptional()
	@IsNumber()
	@Min(1)
	@Type(() => Number)
	page?: number = 1;

	@IsOptional()
	@IsNumber()
	@Min(1)
	@Max(100)
	@Type(() => Number)
	limit?: number = 10;
}
