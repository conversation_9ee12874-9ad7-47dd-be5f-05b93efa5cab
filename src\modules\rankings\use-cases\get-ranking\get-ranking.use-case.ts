import { Inject, Injectable, NotFoundException } from "@nestjs/common";
import { IRanking, IRankingRepository } from "../../models/interfaces";

@Injectable()
export class GetRankingUseCase {
	constructor(
		@Inject("IRankingRepository")
		private rankingRepository: IRankingRepository
	) {}

	async execute(id: string, userId?: string): Promise<IRanking> {
		const ranking = await this.rankingRepository.findByIdWithItems(id);

		if (!ranking) {
			throw new NotFoundException("Ranking não encontrado");
		}

		// Se o ranking não for público e não pertencer ao usuário, não permita acesso
		if (!ranking.isPublic && ranking.userId !== userId) {
			throw new NotFoundException("Ranking não encontrado");
		}

		return ranking;
	}
}
