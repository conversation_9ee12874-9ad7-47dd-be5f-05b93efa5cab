import { Inject, Injectable, NotFoundException, ConflictException } from "@nestjs/common";
import { ITagRepository, IWorkTag, IWorkTagRepository } from "../../models/interfaces";

@Injectable()
export class AddTagToWorkUseCase {
	constructor(
		@Inject("ITagRepository")
		private tagRepository: ITagRepository,

		@Inject("IWorkTagRepository")
		private workTagRepository: IWorkTagRepository,

		@Inject("IWorkRepository")
		private workRepository: any // Usando any pois o IWorkRepository não foi importado
	) {}

	async execute(workId: string, tagId: string): Promise<IWorkTag> {
		// Verificar se o tag existe
		const tag = await this.tagRepository.findById(tagId);

		if (!tag) {
			throw new NotFoundException("Tag não encontrada");
		}

		// Verificar se a obra existe
		const work = await this.workRepository.findById(workId);

		if (!work) {
			throw new NotFoundException("Obra não encontrada");
		}

		// Verificar se a tag já está associada à obra
		const existingWorkTag = await this.workTagRepository.findByWorkIdAndTagId(workId, tagId);

		if (existingWorkTag) {
			throw new ConflictException("Esta tag já está associada à obra");
		}

		// Criar a associação entre obra e tag
		const workTag = await this.workTagRepository.create({ workId, tagId });

		// Incrementar o contador de obras na tag
		await this.tagRepository.incrementWorksCount(tagId);

		return workTag;
	}
}
