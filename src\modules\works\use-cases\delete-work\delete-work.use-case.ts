import { Injectable, NotFoundException } from "@nestjs/common";
import { WorkRepository } from "../../repositories";

@Injectable()
export class DeleteWorkUseCase {
	constructor(private readonly workRepository: WorkRepository) {}

	async execute(id: string): Promise<void> {
		// Verificar se a obra existe
		const existingWork = await this.workRepository.findById(id);
		if (!existingWork) {
			throw new NotFoundException("Obra não encontrada");
		}

		await this.workRepository.delete(id);
	}
}
