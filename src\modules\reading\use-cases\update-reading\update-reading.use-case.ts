import { Injectable, NotFoundException, Inject } from "@nestjs/common";
import { IUserWorkRepository, IUpdateUserWorkRequest, IUserWork } from "../../models/interfaces";

@Injectable()
export class UpdateReadingUseCase {
	constructor(
		@Inject("IUserWorkRepository")
		private readonly userWorkRepository: IUserWorkRepository
	) {}

	async execute(userId: string, userWorkId: string, data: IUpdateUserWorkRequest): Promise<IUserWork> {
		// Verificar se o registro existe e pertence ao usuário
		const userWork = await this.userWorkRepository.findById(userWorkId);
		if (!userWork) {
			throw new NotFoundException("Registro de leitura não encontrado");
		}

		if (userWork.userId !== userId) {
			throw new NotFoundException("Registro de leitura não encontrado");
		}

		return await this.userWorkRepository.update(userWorkId, data);
	}
}
