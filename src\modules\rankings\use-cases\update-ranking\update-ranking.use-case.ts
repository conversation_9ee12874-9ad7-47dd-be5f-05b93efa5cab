import { Inject, Injectable, NotFoundException } from "@nestjs/common";
import { UpdateRankingDto } from "../../models/dtos";
import { IRanking, IRankingRepository } from "../../models/interfaces";

@Injectable()
export class UpdateRankingUseCase {
	constructor(
		@Inject("IRankingRepository")
		private rankingRepository: IRankingRepository
	) {}

	async execute(id: string, userId: string, updateRankingDto: UpdateRankingDto): Promise<IRanking> {
		const existingRanking = await this.rankingRepository.findById(id);

		if (!existingRanking) {
			throw new NotFoundException("Ranking não encontrado");
		}

		// Verificar se o ranking pertence ao usuário
		if (existingRanking.userId !== userId) {
			throw new NotFoundException("Ranking não encontrado");
		}

		return await this.rankingRepository.update(id, updateRankingDto);
	}
}
