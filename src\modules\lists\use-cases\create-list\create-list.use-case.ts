import { Injectable, ConflictException, Inject } from "@nestjs/common";
import { IListRepository, ICreateListRequest, IList } from "../../models/interfaces";

@Injectable()
export class CreateListUseCase {
	constructor(
		@Inject("IListRepository")
		private readonly listRepository: IListRepository
	) {}

	async execute(userId: string, data: Omit<ICreateListRequest, "userId">): Promise<IList> {
		// Verificar se já existe uma lista com o mesmo nome para este usuário
		const existingList = await this.listRepository.findByUserAndName(userId, data.name);
		if (existingList) {
			throw new ConflictException("Já existe uma lista com este nome");
		}

		const createData: ICreateListRequest = {
			...data,
			userId,
		};

		return await this.listRepository.create(createData);
	}
}
