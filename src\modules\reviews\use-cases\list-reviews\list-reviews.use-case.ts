import { Injectable, Inject } from "@nestjs/common";
import { IReviewRepository, IReview, IReviewFilters } from "../../models/interfaces";

@Injectable()
export class ListReviewsUseCase {
	constructor(
		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository,
	) {}

	async execute(filters: IReviewFilters): Promise<IReview[]> {
		return await this.reviewRepository.findMany(filters);
	}
}
