import { Inject, Injectable, NotFoundException } from "@nestjs/common";
import { IRankingItem, IRankingItemRepository, IRankingRepository } from "../../models/interfaces";
import { UpdateRankingItemDto } from "../../models/dtos";

@Injectable()
export class UpdateRankingItemUseCase {
	constructor(
		@Inject("IRankingRepository")
		private rankingRepository: IRankingRepository,

		@Inject("IRankingItemRepository")
		private rankingItemRepository: IRankingItemRepository
	) {}

	async execute(rankingId: string, itemId: string, userId: string, dto: UpdateRankingItemDto): Promise<IRankingItem> {
		// Verificar se o ranking existe e pertence ao usuário
		const ranking = await this.rankingRepository.findById(rankingId);

		if (!ranking) {
			throw new NotFoundException("Ranking não encontrado");
		}

		if (ranking.userId !== userId) {
			throw new NotFoundException("Ranking não encontrado");
		}

		// Verificar se o item existe e pertence ao ranking
		const item = await this.rankingItemRepository.findById(itemId);

		if (!item || item.rankingId !== rankingId) {
			throw new NotFoundException("Item não encontrado no ranking");
		}

		// Atualizar o item
		return await this.rankingItemRepository.update(itemId, dto);
	}
}
