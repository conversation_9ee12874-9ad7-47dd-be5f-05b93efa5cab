import { Injectable, ConflictException } from "@nestjs/common";
import { WorkRepository } from "../../repositories";
import { ICreateWorkRequest, IWork } from "../../models/interfaces";

@Injectable()
export class CreateWorkUseCase {
	constructor(private readonly workRepository: WorkRepository) {}

	async execute(data: ICreateWorkRequest): Promise<IWork> {
		// Verificar se já existe uma obra com o mesmo título
		const existingWork = await this.workRepository.findByTitle(data.title);
		if (existingWork) {
			throw new ConflictException("Uma obra com este título já existe");
		}

		return await this.workRepository.create(data);
	}
}
