import { Injectable, ConflictException, Inject } from "@nestjs/common";
import { IReviewRepository, ICreateReviewRequest, IReview } from "../../models/interfaces";

@Injectable()
export class CreateReviewUseCase {
	constructor(
		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository
	) {}

	async execute(data: ICreateReviewRequest): Promise<IReview> {
		// Verificar se o usuário já fez review desta obra
		const existingReview = await this.reviewRepository.findByUserAndWork(String(data.userId), data.workId);

		if (existingReview) {
			throw new ConflictException("Você já avaliou esta obra");
		}

		return await this.reviewRepository.create(data);
	}
}
