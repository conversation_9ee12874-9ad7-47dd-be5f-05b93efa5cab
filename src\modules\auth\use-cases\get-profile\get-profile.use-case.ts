import { Injectable, Inject, NotFoundException } from "@nestjs/common";
import { IAuthRepository } from "../../models/interfaces/auth-repository.interface";
import { UserResponseDto } from "../../../user/models/dtos/user-response.dto";
import { ICurrentUserPayload } from "../../../../shared/decorators/current-user.decorator";
import { User } from "../../../user/models/entities/user.entity";

@Injectable()
export class GetProfileUseCase {
	constructor(
		@Inject("IAuthRepository")
		private readonly authRepository: IAuthRepository
	) {}

	async execute(currentUser: ICurrentUserPayload): Promise<UserResponseDto> {
		// Buscar o usuário atual com dados atualizados
		const user = await this.authRepository.findByUsernameOrEmail(currentUser.username);
		if (!user) {
			throw new NotFoundException("<PERSON>u<PERSON><PERSON> não encontrado");
		}

		return this.mapToUserResponseDto(user);
	}

	private mapToUserResponseDto(user: User): UserResponseDto {
		const { password, refreshToken, ...userWithoutSensitiveData } = user;
		return userWithoutSensitiveData as UserResponseDto;
	}
}
