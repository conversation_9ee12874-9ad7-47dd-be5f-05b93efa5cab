import { Controller, Get, Post, Put, Delete, Param, Body, Query, UseGuards, Request } from "@nestjs/common";
import { JwtAuthGuard } from "../../auth/guards/jwt-auth.guard";
import {
	CreateReviewUseCase,
	GetReviewUseCase,
	ListReviewsUseCase,
	UpdateReviewUseCase,
	DeleteReviewUseCase,
	GetReviewStatsUseCase,
	ToggleReviewLikeUseCase,
} from "../use-cases";
import { CreateReviewDto, UpdateReviewDto, ReviewFiltersDto } from "../models/dtos";

@Controller("reviews")
export class ReviewsController {
	constructor(
		private readonly createReviewUseCase: CreateReviewUseCase,
		private readonly getReviewUseCase: GetReviewUseCase,
		private readonly listReviewsUseCase: ListReviewsUseCase,
		private readonly updateReviewUseCase: UpdateReviewUseCase,
		private readonly deleteReviewUseCase: DeleteReviewUseCase,
		private readonly getReviewStatsUseCase: GetReviewStatsUseCase,
		private readonly toggleReviewLikeUseCase: ToggleReviewLikeUseCase
	) {}

	@Post()
	@UseGuards(JwtAuthGuard)
	async create(@Body() createReviewDto: CreateReviewDto, @Request() req) {
		const reviewData = {
			...createReviewDto,
			userId: req.user.id,
		};
		return await this.createReviewUseCase.execute(reviewData);
	}

	@Get(":id")
	async findOne(@Param("id") id: string) {
		return await this.getReviewUseCase.execute(id);
	}

	@Get()
	async findMany(@Query() filters: ReviewFiltersDto) {
		const parsedFilters = {
			...filters,
			userId: filters.userId ? Number(filters.userId) : undefined,
		};
		return await this.listReviewsUseCase.execute(parsedFilters);
	}

	@Put(":id")
	@UseGuards(JwtAuthGuard)
	async update(@Param("id") id: string, @Body() updateReviewDto: UpdateReviewDto, @Request() req) {
		return await this.updateReviewUseCase.execute(id, req.user.id, updateReviewDto);
	}

	@Delete(":id")
	@UseGuards(JwtAuthGuard)
	async remove(@Param("id") id: string, @Request() req) {
		await this.deleteReviewUseCase.execute(id, req.user.id);
		return { message: "Review deletado com sucesso" };
	}

	@Get("stats/:workId")
	async getStats(@Param("workId") workId: string) {
		return await this.getReviewStatsUseCase.execute(workId);
	}

	@Post(":id/like")
	@UseGuards(JwtAuthGuard)
	async like(@Param("id") id: string) {
		await this.toggleReviewLikeUseCase.execute(id, true);
		return { message: "Like adicionado com sucesso" };
	}

	@Delete(":id/like")
	@UseGuards(JwtAuthGuard)
	async unlike(@Param("id") id: string) {
		await this.toggleReviewLikeUseCase.execute(id, false);
		return { message: "Like removido com sucesso" };
	}
}
