import { Injectable, NotFoundException, Inject } from "@nestjs/common";
import { IUserWorkRepository } from "../../models/interfaces";

@Injectable()
export class DeleteReadingUseCase {
	constructor(
		@Inject("IUserWorkRepository")
		private readonly userWorkRepository: IUserWorkRepository
	) {}

	async execute(userId: string, userWorkId: string): Promise<void> {
		// Verificar se o registro existe e pertence ao usuário
		const userWork = await this.userWorkRepository.findById(userWorkId);
		if (!userWork) {
			throw new NotFoundException("Registro de leitura não encontrado");
		}

		if (userWork.userId !== userId) {
			throw new NotFoundException("Registro de leitura não encontrado");
		}

		await this.userWorkRepository.delete(userWorkId);
	}
}
