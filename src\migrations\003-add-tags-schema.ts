import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTagsSchema1718478073828 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Criar a tabela de tags
    await queryRunner.query(`
      CREATE TABLE "tags" (
        "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
        "name" varchar(50) NOT NULL,
        "color" varchar(7), 
        "description" text,
        "worksCount" integer NOT NULL DEFAULT 0,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now()
      )
    `);

    // Criar índice para nome (para garantir unicidade)
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_tags_name" ON "tags" ("name")
    `);

    // Criar a tabela de associação entre obras e tags
    await queryRunner.query(`
      CREATE TABLE "work_tags" (
        "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
        "workId" uuid NOT NULL,
        "tagId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "FK_work_tags_tags" FOREIGN KEY ("tagId") REFERENCES "tags" ("id") ON DELETE CASCADE
      )
    `);

    // Criar índice composto para workId + tagId (para garantir unicidade)
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_work_tags_workId_tagId" ON "work_tags" ("workId", "tagId")
    `);

    // Criar índice para tagId (para pesquisas de obras por tag)
    await queryRunner.query(`
      CREATE INDEX "IDX_work_tags_tagId" ON "work_tags" ("tagId")
    `);

    // Criar índice para workId (para pesquisas de tags por obra)
    await queryRunner.query(`
      CREATE INDEX "IDX_work_tags_workId" ON "work_tags" ("workId")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remover índices
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_work_tags_workId"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_work_tags_tagId"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_work_tags_workId_tagId"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_tags_name"`);

    // Remover tabelas
    await queryRunner.query(`DROP TABLE IF EXISTS "work_tags"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "tags"`);
  }
}
