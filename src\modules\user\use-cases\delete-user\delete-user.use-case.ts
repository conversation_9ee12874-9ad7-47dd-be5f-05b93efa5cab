import { Injectable, NotFoundException, Inject } from "@nestjs/common";
import { IUserRepository } from "../../models/interfaces/user-repository.interface";

@Injectable()
export class DeleteUserUseCase {
	constructor(
		@Inject("IUserRepository")
		private readonly userRepository: IUserRepository
	) {}

	async execute(id: number): Promise<void> {
		const user = await this.userRepository.findById(id);
		if (!user) {
			throw new NotFoundException("Usuário não encontrado");
		}

		await this.userRepository.delete(id);
	}
}
