import { Injectable, NotFoundException, ForbiddenException, Inject } from "@nestjs/common";
import { IReviewRepository } from "../../models/interfaces";

@Injectable()
export class DeleteReviewUseCase {
	constructor(
		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository
	) {}

	async execute(id: string, userId: string): Promise<void> {
		const review = await this.reviewRepository.findById(id);

		if (!review) {
			throw new NotFoundException("Review não encontrado");
		}

		if (review.userId !== Number(userId)) {
			throw new ForbiddenException("Você só pode deletar seus próprios reviews");
		}

		await this.reviewRepository.delete(id);
	}
}
