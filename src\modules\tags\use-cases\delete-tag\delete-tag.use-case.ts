import { Inject, Injectable, NotFoundException } from "@nestjs/common";
import { ITagRepository } from "../../models/interfaces";

@Injectable()
export class DeleteTagUseCase {
	constructor(
		@Inject("ITagRepository")
		private tagRepository: ITagRepository
	) {}

	async execute(id: string): Promise<void> {
		const tag = await this.tagRepository.findById(id);

		if (!tag) {
			throw new NotFoundException("Tag não encontrada");
		}

		await this.tagRepository.delete(id);
	}
}
