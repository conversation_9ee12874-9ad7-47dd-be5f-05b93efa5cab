import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Review } from "../models/entities";
import { IReviewRepository, IReview, ICreateReviewRequest, IUpdateReviewRequest, IReviewFilters, IReviewStats } from "../models/interfaces";
import { ReviewStatus } from "../models/enums";

@Injectable()
export class ReviewRepository implements IReviewRepository {
	constructor(
		@InjectRepository(Review)
		private readonly reviewRepository: Repository<Review>
	) {}

	async create(data: ICreateReviewRequest): Promise<IReview> {
		const review = this.reviewRepository.create(data);
		return await this.reviewRepository.save(review);
	}

	async findById(id: string): Promise<IReview | null> {
		return await this.reviewRepository.findOne({
			where: { id },
			relations: ["work", "user"],
		});
	}

	async findByUserAndWork(userId: string, workId: string): Promise<IReview | null> {
		return await this.reviewRepository.findOne({
			where: { userId: Number(userId), workId },
			relations: ["work", "user"],
		});
	}

	async findMany(filters: IReviewFilters): Promise<IReview[]> {
		const queryBuilder = this.reviewRepository
			.createQueryBuilder("review")
			.leftJoinAndSelect("review.work", "work")
			.leftJoinAndSelect("review.user", "user");

		if (filters.workId) {
			queryBuilder.andWhere("review.workId = :workId", { workId: filters.workId });
		}

		if (filters.userId) {
			queryBuilder.andWhere("review.userId = :userId", { userId: filters.userId });
		}

		if (filters.rating) {
			queryBuilder.andWhere("review.rating = :rating", { rating: filters.rating });
		}

		if (filters.status) {
			queryBuilder.andWhere("review.status = :status", { status: filters.status });
		} else {
			queryBuilder.andWhere("review.status = :status", { status: ReviewStatus.ACTIVE });
		}

		// Ordenação
		const sortBy = filters.sortBy || "createdAt";
		const sortOrder = filters.sortOrder || "DESC";
		queryBuilder.orderBy(`review.${sortBy}`, sortOrder);

		// Paginação
		const page = filters.page || 1;
		const limit = filters.limit || 10;
		const skip = (page - 1) * limit;

		queryBuilder.skip(skip).take(limit);

		return await queryBuilder.getMany();
	}

	async update(id: string, data: IUpdateReviewRequest): Promise<IReview> {
		await this.reviewRepository.update(id, data);
		return await this.findById(id);
	}

	async delete(id: string): Promise<void> {
		await this.reviewRepository.update(id, { status: ReviewStatus.DELETED });
	}

	async getStats(workId: string): Promise<IReviewStats> {
		const reviews = await this.reviewRepository.find({
			where: { workId, status: ReviewStatus.ACTIVE },
		});

		const totalReviews = reviews.length;
		const averageRating = totalReviews > 0 ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews : 0;

		const ratingDistribution = {
			1: 0,
			2: 0,
			3: 0,
			4: 0,
			5: 0,
		};

		reviews.forEach(review => {
			ratingDistribution[review.rating]++;
		});

		return {
			totalReviews,
			averageRating: Math.round(averageRating * 100) / 100,
			ratingDistribution,
		};
	}

	async incrementLikes(id: string): Promise<void> {
		await this.reviewRepository.increment({ id }, "likesCount", 1);
	}

	async decrementLikes(id: string): Promise<void> {
		await this.reviewRepository.decrement({ id }, "likesCount", 1);
	}

	async incrementDislikes(id: string): Promise<void> {
		await this.reviewRepository.increment({ id }, "dislikesCount", 1);
	}

	async decrementDislikes(id: string): Promise<void> {
		await this.reviewRepository.decrement({ id }, "dislikesCount", 1);
	}
}
