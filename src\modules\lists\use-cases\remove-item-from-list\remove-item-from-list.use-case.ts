import { Injectable, NotFoundException, Inject } from "@nestjs/common";
import { IListRepository, IListItemRepository } from "../../models/interfaces";

@Injectable()
export class RemoveItemFromListUseCase {
	constructor(
		@Inject("IListRepository")
		private readonly listRepository: IListRepository,
		@Inject("IListItemRepository")
		private readonly listItemRepository: IListItemRepository
	) {}

	async execute(userId: string, listId: string, itemId: string): Promise<void> {
		// Verificar se a lista existe e pertence ao usuário
		const list = await this.listRepository.findById(listId);
		if (!list) {
			throw new NotFoundException("Lista não encontrada");
		}

		if (list.userId !== userId) {
			throw new NotFoundException("Lista não encontrada");
		}

		// Verificar se o item existe e pertence à lista
		const item = await this.listItemRepository.findById(itemId);
		if (!item) {
			throw new NotFoundException("Item não encontrado");
		}

		if (item.listId !== listId) {
			throw new NotFoundException("Item não encontrado");
		}

		await this.listItemRepository.delete(itemId);

		// Decrementar contador de itens na lista
		await this.listRepository.decrementItemsCount(listId);
	}
}
