import { Injectable, NotFoundException, ConflictException } from "@nestjs/common";
import { WorkRepository } from "../../repositories";
import { IUpdateWorkRequest, IWork } from "../../models/interfaces";

@Injectable()
export class UpdateWorkUseCase {
	constructor(private readonly workRepository: WorkRepository) {}

	async execute(id: string, data: IUpdateWorkRequest): Promise<IWork> {
		// Verificar se a obra existe
		const existingWork = await this.workRepository.findById(id);
		if (!existingWork) {
			throw new NotFoundException("Obra não encontrada");
		}

		// Se está tentando alterar o título, verificar se não existe outra obra com o mesmo título
		if (data.title && data.title !== existingWork.title) {
			const workWithSameTitle = await this.workRepository.findByTitle(data.title);
			if (workWithSameTitle) {
				throw new ConflictException("Uma obra com este título já existe");
			}
		}

		return await this.workRepository.update(id, data);
	}
}
